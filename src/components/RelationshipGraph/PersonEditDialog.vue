<template>
  <div class="dialog-overlay">
    <div class="dialog-container edit-dialog">
      <div class="dialog-header">
        <div class="dialog-title">编辑人员信息</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>
      <div class="dialog-content edit-content">
        <!-- 头像和称呼组合区域 -->
        <div class="input-group avatar-name-group">
          <!-- 标签行 -->
          <div class="labels-row">
            <label class="input-label avatar-label">头像</label>
            <label class="input-label name-label">您的称呼 *</label>
          </div>
          <!-- 内容行 -->
          <div class="content-row">
            <div class="avatar-section">
              <div class="avatar-upload-wrapper">
                <AvatarUpload
                  ref="avatarUploadRef"
                  v-model="editForm.avatar"
                  :size="50"
                  placeholder="上传头像"
                  :max-size="10"
                  @upload-success="onAvatarUploadSuccess"
                  @upload-error="onAvatarUploadError"
                  @show-avatar-selection="showAvatarSelectionDialog = true"
                />
              </div>
            </div>
            <div class="name-section">
              <input
                v-model="editForm.canonical_name"
                type="text"
                class="input-field"
                placeholder="请输入您的称呼"
                maxlength="50"
                @input="onEditFormChange"
              />
            </div>
          </div>
        </div>
        <div class="input-group">
          <label class="input-label">别名</label>
          <input
            v-model="editForm.aliases"
            type="text"
            class="input-field"
            placeholder="请输入别名"
            maxlength="100"
            @input="onEditFormChange"
          />
        </div>
        <div class="input-group">
          <label class="input-label">个人简介</label>
          <textarea
            v-model="editForm.profile_summary"
            class="textarea-field"
            placeholder="请输入个人简介"
            rows="3"
            maxlength="500"
            @input="onEditFormChange"
          ></textarea>
        </div>
        <div class="input-group">
          <label class="input-label">关键属性</label>
          <div class="key-attributes-container">
            <div v-for="(_value, key) in editForm.key_attributes" :key="key" class="attribute-item">
              <input :value="key" type="text" class="attribute-key" :placeholder="key" readonly />
              <input
                v-model="editForm.key_attributes[key]"
                type="text"
                class="attribute-value"
                :placeholder="`请输入${key}`"
                maxlength="100"
                @input="onEditFormChange"
              />
            </div>
            <!-- 新属性输入框 -->
            <div v-for="attr in newAttributes" :key="attr.id" class="add-attribute-container">
              <input
                v-model="attr.key"
                type="text"
                class="attribute-key"
                placeholder="属性名称"
                maxlength="20"
                @input="onEditFormChange"
              />
              <input
                v-model="attr.value"
                type="text"
                class="attribute-value"
                placeholder="属性值"
                maxlength="100"
                @input="onEditFormChange"
              />
              <button class="remove-attribute-btn" @click="cancelAddAttribute(attr.id)">×</button>
            </div>
            <!-- 添加属性按钮 -->
            <div class="add-attribute-btn-container">
              <button class="add-attribute-btn" @click="showAddAttributeInput">+ 添加属性</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="dialog-footer">
        <button class="cancel-btn" :disabled="isUpdating" @click="handleCancel">取消</button>
        <button class="confirm-btn" :disabled="isUpdating || !isFormValid" @click="handleConfirmSave">
          <span v-if="isUpdating">保存中...</span>
          <span v-else>确认修改</span>
        </button>
      </div>
    </div>

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { updatePerson, type IPersonData } from '@/apis/relation';
import { showFailToast, showSuccessToast } from 'vant';
import AvatarUpload from '@/components/AvatarUpload.vue';
import AvatarSelectionDialog from '@/components/AvatarSelectionDialog.vue';

// Props定义
interface IProps {
  person: IPersonData | null;
  userId: string;
  isUserProfile?: boolean; // 新增：标识是否为用户档案（核心节点）
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 响应式数据
const isUpdating = ref(false);
const showAvatarSelectionDialog = ref(false);
const avatarUploadRef = ref();

// 编辑表单数据
const editForm = ref({
  canonical_name: '',
  aliases: '',
  relationships: [] as string[],
  profile_summary: '',
  key_attributes: {} as Record<string, string>,
  avatar: '',
});

// 新属性相关数据 - 支持多个新属性输入框
const newAttributes = ref<Array<{ id: string; key: string; value: string }>>([]);

// 默认关键属性键列表 - 与PersonList保持一致
const defaultAttributeKeys = ['居住地', '性别', '生日', '联系方式', '兴趣', '期望', '旅游历史', '饮食偏好', '过往历史'];

// 初始化默认关键属性
const initializeDefaultAttributes = () => {
  const defaultAttributes: Record<string, string> = {};
  defaultAttributeKeys.forEach((key) => {
    defaultAttributes[key] = '';
  });
  return defaultAttributes;
};

// 表单验证 - 检查必填字段
const isFormValid = computed(() => {
  return editForm.value.canonical_name.trim().length > 0;
});

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11);
};

// 显示新属性输入框
const showAddAttributeInput = () => {
  newAttributes.value.push({
    id: generateId(),
    key: '',
    value: '',
  });
};

// 取消添加新属性
const cancelAddAttribute = (id: string) => {
  newAttributes.value = newAttributes.value.filter((a) => a.id !== id);
};

// 编辑表单内容更改时的处理函数
const onEditFormChange = () => {
  // 不再自动提交，表单将在弹窗关闭时提交
  // 这里可以添加一些实时验证逻辑，但不提交表单
};

// 头像上传成功处理
const onAvatarUploadSuccess = (url: string) => {
  console.log('✅ [PersonEditDialog] 头像上传成功:', url);
  editForm.value.avatar = url;
  // 不再自动保存，等待用户点击确认修改按钮
};

// 头像上传失败处理
const onAvatarUploadError = (error: string) => {
  console.error('❌ [PersonEditDialog] 头像上传失败:', error);
};

// 处理头像选择
const handleAvatarSelect = (selectedAvatarId: string) => {
  console.log('✅ [PersonEditDialog] 选择默认头像:', selectedAvatarId);
  editForm.value.avatar = selectedAvatarId;
  showAvatarSelectionDialog.value = false;
  // 不再自动保存，等待用户点击确认修改按钮
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发AvatarUpload组件的文件选择
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理取消 - 直接关闭弹窗，不保存
const handleCancel = () => {
  emit('close');
};

// 处理弹窗关闭 - 直接关闭弹窗，不保存
const handleClose = () => {
  handleCancel();
};

// 处理确认保存
const handleConfirmSave = async () => {
  await submitEditForm();
};

// 提交编辑表单
const submitEditForm = async () => {
  if (!props.person || !editForm.value.canonical_name.trim() || isUpdating.value) {
    return;
  }

  try {
    isUpdating.value = true;
    console.log('🔄 [PersonEditDialog.vue] 提交编辑表单...', {
      userId: props.userId,
      personId: props.person.person_id,
      editForm: editForm.value,
    });

    // 合并新属性到key_attributes
    const finalKeyAttributes: Record<string, string> = {};

    // 处理默认属性：只有当 attribute-value 不为空时才保存
    Object.entries(editForm.value.key_attributes).forEach(([key, value]) => {
      if (value && value.trim()) {
        finalKeyAttributes[key] = value.trim();
      }
    });

    // 处理新增属性：只有当 attribute-key 不为空时才保存新增的属性
    // 当 attribute-key 为空，attribute-value 不为空时，不保存
    // 当 attribute-key 不为空，attribute-value 为空时，保存
    newAttributes.value.forEach((attr) => {
      if (attr.key.trim()) {
        finalKeyAttributes[attr.key.trim()] = attr.value.trim();
      }
    });

    // 处理aliases字段，提交时添加[]和双引号
    let submitAliases = editForm.value.aliases.trim();
    if (submitAliases === '') {
      submitAliases = '[]';
    } else {
      // 按、分割别名，为每个别名添加双引号，然后用逗号连接，最后添加[]
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(props.person.person_id, {
      user_id: props.userId,
      canonical_name: editForm.value.canonical_name.trim(),
      aliases: submitAliases,
      relationships: editForm.value.relationships,
      profile_summary: editForm.value.profile_summary.trim(),
      key_attributes: finalKeyAttributes,
      is_user: props.isUserProfile || false, // 根据是否为核心节点设置is_user参数
      avatar: editForm.value.avatar,
    });

    console.log('📡 [PersonEditDialog.vue] 更新人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [PersonEditDialog.vue] 人员更新成功');
      showSuccessToast(`与${editForm.value.canonical_name}的关系更新成功了喔～`);

      // 通知父组件更新成功
      emit('success');

      // 清空新属性数组，因为已经合并到 key_attributes 中
      newAttributes.value = [];

      // 关闭弹窗
      emit('close');
    } else {
      console.warn('⚠️ [PersonEditDialog.vue] 更新人员失败:', response);
      showFailToast('更新人员失败');
    }
  } catch (error) {
    console.error('❌ [PersonEditDialog.vue] 更新人员失败:', error);
    showFailToast('更新人员失败');
  } finally {
    isUpdating.value = false;
  }
};

// 监听person变化，初始化表单数据
watch(
  () => props.person,
  (newPerson) => {
    if (newPerson) {
      // 初始化默认属性，然后合并现有属性值
      const keyAttributes: Record<string, string> = initializeDefaultAttributes();

      if (newPerson.key_attributes) {
        Object.entries(newPerson.key_attributes).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            keyAttributes[key] = String(value);
          }
        });
      }

      // 处理relationships字段，确保它是数组格式
      let relationships: string[] = [];
      if (newPerson.relationships) {
        if (typeof newPerson.relationships === 'string') {
          try {
            relationships = JSON.parse(newPerson.relationships);
          } catch (parseError) {
            console.warn('⚠️ [PersonEditDialog.vue] relationships JSON解析失败:', parseError);
            relationships = [];
          }
        } else {
          relationships = newPerson.relationships;
        }
      }

      // 处理aliases字段，显示时去除[]和双引号，用、分割
      let displayAliases = newPerson.aliases || '';
      if (displayAliases === '[]') {
        displayAliases = '';
      } else if (displayAliases.startsWith('[') && displayAliases.endsWith(']')) {
        // 去除首尾的[]
        displayAliases = displayAliases.replace(/^\[|\]$/g, '');
        // 去除每个别名外面的双引号，并用、分割
        if (displayAliases.trim()) {
          // 按逗号分割，去除每项的双引号和空格，然后用、连接
          displayAliases = displayAliases
            .split(',')
            .map((alias) => alias.trim().replace(/^"|"$/g, ''))
            .filter((alias) => alias.length > 0)
            .join('、');
        }
      }

      editForm.value = {
        canonical_name: newPerson.canonical_name,
        aliases: displayAliases,
        relationships,
        profile_summary: newPerson.profile_summary || '',
        key_attributes: keyAttributes,
        avatar: newPerson.avatar || '',
      };

      // 重置新属性数组
      newAttributes.value = [];
    }
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.edit-dialog {
    max-width: 600px;
    height: 900px;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &.edit-content {
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    background: rgba(0, 188, 212, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &.avatar-name-group {
      .labels-row {
        display: flex;
        gap: 24px;
        margin-bottom: 12px;

        .avatar-label {
          width: 120px;
          text-align: left;
          flex-shrink: 0;
          padding-left: 18px;
        }

        .name-label {
          flex: 1;
        }
      }

      .content-row {
        display: flex;
        gap: 24px;
        align-items: flex-end;

        .avatar-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          flex-shrink: 0;
          width: 120px;

          .avatar-upload-wrapper {
            display: flex;
            justify-content: flex-start;
          }
        }

        .name-section {
          flex: 1;

          .input-field {
            width: 100%;
          }
        }
      }
    }

    .input-label {
      color: white;
      font-size: 30px; // 增加8px (原来22px)
      font-weight: 600;
      margin-bottom: 8px;
    }

    .input-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    .textarea-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    .key-attributes-container {
      width: 100%;
      overflow: visible;
      padding-top: 8px;
      .attribute-item,
      .add-attribute-container {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        align-items: center;
        width: 100%;
        box-sizing: border-box;

        .attribute-key {
          width: 160px;
          flex-shrink: 0;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 12px;
          padding: 14px 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 28px; // 增加8px (原来24px)
          box-sizing: border-box;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &[readonly] {
            background: rgba(255, 255, 255, 0.05);
            cursor: not-allowed;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          }
        }

        .attribute-value {
          flex: 1;
          min-width: 0; /* 确保可以收缩 */
          max-width: calc(100% - 140px - 36px - 28px); /* 减去 attribute-key 宽度、按钮宽度和间距 */
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 12px;
          padding: 14px 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 32px; // 增加8px (原来24px)
          box-sizing: border-box;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          }
        }

        .remove-attribute-btn {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          border: 1px solid rgba(239, 68, 68, 0.3);
          background: rgba(239, 68, 68, 0.2);
          color: #ef4444;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28px; // 增加8px (原来20px)
          font-weight: bold;
          transition: all 0.2s ease;
          flex-shrink: 0;

          &:hover {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);
            transform: scale(1.05);
          }
        }
      }

      .add-attribute-btn-container {
        margin-top: 16px;

        .add-attribute-btn {
          width: 100%;
          background: transparent;
          color: #00bcd4;
          border: 2px solid #00bcd4;
          border-radius: 20px;
          padding: 16px 16px;
          font-size: 36px; // 增加8px (原来28px)
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 60px;

          &:hover {
            background: rgba(0, 188, 212, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: transparent;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
